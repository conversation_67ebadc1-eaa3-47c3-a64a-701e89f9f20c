/**
 * Utilities for generating MDC (Markdown Component) formatted content from Rule objects
 */

import { Rule } from '@/lib/store';

/**
 * Generate MDC (Markdown Component) formatted text from a Rule object
 * 
 * @param rule - The Rule object to convert to MDC format
 * @returns MDC formatted string with frontmatter and content
 */
export function generateMDCFromRule(rule: Rule): string {
  const tags = rule.tags.map(t => t.tag.name);
  const author = rule.user?.name || 'Unknown';
  const createdDate = new Date(rule.createdAt).toLocaleDateString();
  const updatedDate = new Date(rule.updatedAt).toLocaleDateString();
  const createdISO = new Date(rule.createdAt).toISOString();
  const updatedISO = new Date(rule.updatedAt).toISOString();
  
  // Generate MDC frontmatter and content
  const mdc = `---
id: "${rule.id}"
name: "${rule.title}"
description: "${rule.description || ''}"
author: "${author}"
createdAt: "${createdISO}"
updatedAt: "${updatedISO}"
ideType: "${rule.ideType}"
visibility: "${rule.visibility}"
applyType: "${rule.applyType}"
tags: [${tags.map(tag => `"${tag}"`).join(', ')}]
---

# ${rule.title}

${rule.description ? `> ${rule.description}\n\n` : ''}

## Metadata

- **IDE Type:** ${rule.ideType}
- **Apply Type:** ${rule.applyType}
- **Author:** ${author}
- **Created:** ${createdDate}
- **Updated:** ${updatedDate}
${tags.length > 0 ? `- **Tags:** ${tags.join(', ')}` : ''}

## Rule Content

${rule.content}
`;

  return mdc;
}

/**
 * Generate MDC formatted text from multiple Rule objects
 * 
 * @param rules - Array of Rule objects to convert to MDC format
 * @param options - Optional configuration for the output
 * @returns Combined MDC formatted string with all rules
 */
export function generateMDCFromRules(
  rules: Rule[], 
  options: {
    includeFileSeparators?: boolean;
    includeRuleIndex?: boolean;
    title?: string;
    description?: string;
  } = {}
): string {
  const { 
    includeFileSeparators = true, 
    includeRuleIndex = false,
    title,
    description 
  } = options;

  let content = '';

  // Add optional header
  if (title || description) {
    content += `# ${title || 'Rules Collection'}\n\n`;
    if (description) {
      content += `${description}\n\n`;
    }
    content += `---\n\n`;
  }

  const mdcRules = rules.map((rule, index) => {
    let ruleContent = generateMDCFromRule(rule);
    
    if (includeRuleIndex) {
      ruleContent = `<!-- Rule ${index + 1}: ${rule.title} -->\n${ruleContent}`;
    }
    
    return ruleContent;
  });

  if (includeFileSeparators) {
    content += mdcRules.join('\n\n<!-- ============================================ -->\n\n');
  } else {
    content += mdcRules.join('\n\n');
  }

  return content;
}

/**
 * Generate a sanitized filename from a rule title
 * 
 * @param title - The rule title to sanitize
 * @param extension - File extension to append (default: 'mdc')
 * @returns Sanitized filename
 */
export function sanitizeRuleFilename(title: string, extension: string = 'mdc'): string {
  return title
    .replace(/[^a-z0-9]/gi, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase()
    .substring(0, 100) + `.${extension}`;
}

/**
 * Copy Rule as MDC formatted text to clipboard
 * 
 * @param rule - The Rule object to copy
 * @returns Promise<boolean> - Success status
 */
export async function copyRuleAsMDCToClipboard(rule: Rule): Promise<boolean> {
  try {
    const mdcText = generateMDCFromRule(rule);
    await navigator.clipboard.writeText(mdcText);
    return true;
  } catch (error) {
    console.error('Failed to copy rule as MDC to clipboard:', error);
    return false;
  }
}

/**
 * Download Rule as MDC file
 * 
 * @param rule - The Rule object to download
 * @param filename - Optional custom filename
 */
export function downloadRuleAsMDC(rule: Rule, filename?: string): void {
  const mdcContent = generateMDCFromRule(rule);
  const finalFilename = filename || sanitizeRuleFilename(rule.title);
  
  const blob = new Blob([mdcContent], {
    type: 'text/markdown',
  });
  
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = finalFilename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Download multiple Rules as a combined MDC file
 * 
 * @param rules - Array of Rule objects to download
 * @param filename - Optional custom filename
 * @param options - Optional configuration for the output
 */
export function downloadRulesAsMDC(
  rules: Rule[], 
  filename?: string,
  options?: {
    includeFileSeparators?: boolean;
    includeRuleIndex?: boolean;
    title?: string;
    description?: string;
  }
): void {
  const mdcContent = generateMDCFromRules(rules, options);
  const finalFilename = filename || `rules-collection-${new Date().toISOString().split('T')[0]}.mdc`;
  
  const blob = new Blob([mdcContent], {
    type: 'text/markdown',
  });
  
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = finalFilename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
