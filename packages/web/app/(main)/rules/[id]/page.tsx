"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Card, Badge, Button, TextField, TextArea, Select, Text } from "@radix-ui/themes";
import { Copy, ArrowLeft, Download, Share, ExternalLink, Edit, Save, X } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { useRule, useUpdateRule } from "@/hooks/use-rule-queries";
import { useSession } from "@/lib/auth-client";
import { IDEType, VisibilityType, ApplyType } from "@/lib/store";

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

export default function RulePage() {
  const params = useParams();
  const ruleId = params.id as string;
  const { data: session } = useSession();
  const [isEditing, setIsEditing] = useState(false);

  // Use the query hook instead of manual fetch
  const {
    data: rule,
    isLoading,
    error,
    isError
  } = useRule(ruleId);

  // Update rule mutation
  const updateRuleMutation = useUpdateRule();

  // Check if user is the owner of the rule
  const isOwner = session?.user?.id === rule?.userId;

  // Form state for editing
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    content: "",
    ideType: "GENERAL" as IDEType,
    visibility: "PRIVATE" as VisibilityType,
    applyType: "manual" as ApplyType,
    tags: [] as string[]
  });

  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    title: "",
    content: ""
  });

  // Initialize form data when rule loads or editing starts
  useEffect(() => {
    if (rule && isEditing) {
      setFormData({
        title: rule.title,
        description: rule.description || "",
        content: rule.content,
        ideType: rule.ideType,
        visibility: rule.visibility,
        applyType: rule.applyType,
        tags: rule.tags.map(t => t.tag.name)
      });
    }
  }, [rule, isEditing]);

  // Validate form
  const validateForm = () => {
    const errors = { title: "", content: "" };
    let isValid = true;

    if (!formData.title.trim()) {
      errors.title = "Title is required";
      isValid = false;
    }

    if (!formData.content.trim()) {
      errors.content = "Content is required";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Handle save
  const handleSave = async () => {
    if (!rule) return;

    // Validate form
    if (!validateForm()) {
      toast.error("Please fix the validation errors");
      return;
    }

    try {
      // Create API-compatible data structure
      const updateData = {
        title: formData.title.trim(),
        description: formData.description.trim() || null,
        content: formData.content.trim(),
        ideType: formData.ideType,
        visibility: formData.visibility,
        applyType: formData.applyType,
        tags: formData.tags.filter(tag => tag.trim().length > 0)
      };

      await updateRuleMutation.mutateAsync({
        id: rule.id,
        data: updateData as any
      });
      setIsEditing(false);
      setFormErrors({ title: "", content: "" }); // Clear errors on success
      toast.success("Rule updated successfully");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update rule";
      toast.error(errorMessage);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data to original values
    if (rule) {
      setFormData({
        title: rule.title,
        description: rule.description || "",
        content: rule.content,
        ideType: rule.ideType,
        visibility: rule.visibility,
        applyType: rule.applyType,
        tags: rule.tags.map(t => t.tag.name)
      });
    }
  };

  const handleCopyContent = async () => {
    if (!rule) return;
    try {
      await navigator.clipboard.writeText(rule.content);
      toast.success("Rule content copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy content");
    }
  };

  const handleCopyLink = async () => {
    try {
      const url = `${window.location.origin}/r/${ruleId}`;
      await navigator.clipboard.writeText(url);
      toast.success("Rule link copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  const handleDownload = () => {
    if (!rule) return;
    const blob = new Blob([rule.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${rule.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("Rule downloaded");
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading Rule...</h1>
          <p className="text-muted-foreground">Rule ID: {ruleId}</p>
          <div className="mt-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError || !rule) {
    const errorMessage = error instanceof Error 
      ? error.message 
      : "Rule not found";
    
    return (
      <div className="container max-w-4xl py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
          <p className="text-muted-foreground mb-4">{errorMessage}</p>
          <Button variant="outline" asChild>
            <Link href="/rules">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Rules
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-8 space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2 flex-1 mr-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Button variant="ghost" size="1" asChild>
              <Link href="/rules">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <span>Rules</span>
          </div>

          {isEditing ? (
            <div className="space-y-3">
              <div>
                <TextField.Root
                  value={formData.title}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, title: e.target.value }));
                    if (formErrors.title) {
                      setFormErrors(prev => ({ ...prev, title: "" }));
                    }
                  }}
                  placeholder="Rule title"
                  className="text-3xl font-bold"
                  size="3"
                  color={formErrors.title ? "red" : undefined}
                />
                {formErrors.title && (
                  <Text size="1" color="red" className="mt-1 block">
                    {formErrors.title}
                  </Text>
                )}
              </div>
              <TextArea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Rule description (optional)"
                className="text-lg"
                rows={2}
              />
            </div>
          ) : (
            <>
              <h1 className="text-3xl font-bold">{rule.title}</h1>
              {rule.description && (
                <p className="text-lg text-muted-foreground">{rule.description}</p>
              )}
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          {isOwner && isEditing ? (
            <>
              <Button
                variant="solid"
                size="2"
                onClick={handleSave}
                disabled={updateRuleMutation.isPending}
              >
                <Save className="mr-2 h-4 w-4" />
                {updateRuleMutation.isPending ? "Saving..." : "Save"}
              </Button>
              <Button variant="outline" size="2" onClick={handleCancel}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </>
          ) : (
            <>
              {isOwner && (
                <Button variant="solid" size="2" onClick={() => setIsEditing(true)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              )}
              <Button variant="outline" size="2" onClick={handleCopyContent}>
                <Copy className="mr-2 h-4 w-4" />
                Copy
              </Button>
              <Button variant="outline" size="2" onClick={handleCopyLink}>
                <Share className="mr-2 h-4 w-4" />
                Share
              </Button>
              <Button variant="outline" size="2" onClick={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Metadata */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">IDE:</span>
            {isEditing ? (
              <Select.Root
                value={formData.ideType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, ideType: value as IDEType }))}
              >
                <Select.Trigger className="w-32" />
                <Select.Content>
                  <Select.Item value="GENERAL">General</Select.Item>
                  <Select.Item value="CURSOR">Cursor</Select.Item>
                  <Select.Item value="AUGMENT">Augment Code</Select.Item>
                  <Select.Item value="WINDSURF">Windsurf</Select.Item>
                  <Select.Item value="CLAUDE">Claude</Select.Item>
                  <Select.Item value="GITHUB_COPILOT">GitHub Copilot</Select.Item>
                  <Select.Item value="GEMINI">Gemini</Select.Item>
                  <Select.Item value="OPENAI_CODEX">OpenAI Codex</Select.Item>
                  <Select.Item value="CLINE">Cline</Select.Item>
                  <Select.Item value="JUNIE">Junie</Select.Item>
                  <Select.Item value="TRAE">Trae</Select.Item>
                  <Select.Item value="LINGMA">Lingma</Select.Item>
                  <Select.Item value="KIRO">Kiro</Select.Item>
                  <Select.Item value="TENCENT_CODEBUDDY">Tencent Cloud CodeBuddy</Select.Item>
                </Select.Content>
              </Select.Root>
            ) : (
              <Badge variant="outline">{rule.ideType}</Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Visibility:</span>
            {isEditing ? (
              <Select.Root
                value={formData.visibility}
                onValueChange={(value) => setFormData(prev => ({ ...prev, visibility: value as VisibilityType }))}
              >
                <Select.Trigger className="w-24" />
                <Select.Content>
                  <Select.Item value="PRIVATE">Private</Select.Item>
                  <Select.Item value="PUBLIC">Public</Select.Item>
                </Select.Content>
              </Select.Root>
            ) : (
              <Badge variant={rule.visibility === 'PUBLIC' ? 'solid' : 'outline'}>
                {rule.visibility}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Apply Type:</span>
            {isEditing ? (
              <Select.Root
                value={formData.applyType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, applyType: value as ApplyType }))}
              >
                <Select.Trigger className="w-24" />
                <Select.Content>
                  <Select.Item value="manual">Manual</Select.Item>
                  <Select.Item value="auto">Auto</Select.Item>
                  <Select.Item value="always">Always</Select.Item>
                </Select.Content>
              </Select.Root>
            ) : (
              <Badge variant="outline">{rule.applyType}</Badge>
            )}
          </div>

          {rule.user && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Author:</span>
              <span className="text-sm">{rule.user.name || rule.user.email}</span>
            </div>
          )}
        </div>

        {/* Tags */}
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm font-medium">Tags:</span>
            {isEditing ? (
              <TextField.Root
                value={formData.tags.join(', ')}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
                }))}
                placeholder="Enter tags separated by commas"
                className="flex-1"
              />
            ) : (
              rule.tags && rule.tags.length > 0 ? (
                rule.tags.map((tagRelation) => (
                  <Badge
                    key={tagRelation.tag.id}
                    variant="soft"
                    style={{
                      backgroundColor: tagRelation.tag.color + '20',
                      color: tagRelation.tag.color
                    }}
                  >
                    {tagRelation.tag.name}
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">No tags</span>
              )
            )}
          </div>
        </div>
      </Card>

      {/* Rule Content */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Rule Content</h2>
            {!isEditing && (
              <Button variant="ghost" size="1" onClick={handleCopyContent}>
                <Copy className="h-4 w-4" />
              </Button>
            )}
          </div>

          {isEditing ? (
            <div>
              <TextArea
                value={formData.content}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, content: e.target.value }));
                  if (formErrors.content) {
                    setFormErrors(prev => ({ ...prev, content: "" }));
                  }
                }}
                placeholder="Enter your rule content here..."
                className="min-h-[300px] font-mono text-sm"
                rows={15}
                color={formErrors.content ? "red" : undefined}
              />
              {formErrors.content && (
                <Text size="1" color="red" className="mt-1 block">
                  {formErrors.content}
                </Text>
              )}
            </div>
          ) : (
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border">
              <pre className="whitespace-pre-wrap text-sm font-mono overflow-x-auto">
                {rule.content}
              </pre>
            </div>
          )}
        </div>
      </Card>

      {/* Raw Data Link */}
      {rule.visibility === 'PUBLIC' && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Raw Data Access</h3>
              <p className="text-sm text-muted-foreground">
                Access the raw rule content directly via API
              </p>
            </div>
            <Button variant="outline" size="2" asChild>
              <Link href={`/api/rules/raw?id=${rule.id}`} target="_blank">
                <ExternalLink className="mr-2 h-4 w-4" />
                Raw Data
              </Link>
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}

